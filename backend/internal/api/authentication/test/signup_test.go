package test

import (
	"bytes"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/tajjjjr/social-network/backend/internal/api/authentication"
)

type mockDB struct{}

func (m *mockDB) QueryRow(query string, args ...interface{}) *mockRow {
	return &mockRow{}
}

func (m *mockDB) Exec(query string, args ...interface{}) (interface{}, error) {
	return nil, nil
}

type mockRow struct{}

func (r *mockRow) Scan(dest ...interface{}) error {
	if len(dest) > 0 {
		if count, ok := dest[0].(*int); ok {
			*count = 0 // Simulate user does not exist
		}
	}
	return nil
}

func TestSignupHandler_Success(t *testing.T) {
	form := "email=<EMAIL>&password=Password1!&firstName=John&lastName=Doe&dob=2000-01-01&nickname=johndoe&aboutMe=Hello&profileVisibility=public"
	req := httptest.NewRequest("POST", "/register", bytes.NewBufferString(form))
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	w := httptest.NewRecorder()

	// Use mockDB for testing
	authentication.SignupHandler(w, req, nil) // You would inject mockDB in a real test
	resp := w.Result()
	if resp.StatusCode != http.StatusOK {
		t.Errorf("expected status %d, got %d", http.StatusOK, resp.StatusCode)
	}
}

func TestUserExists_UserDoesNotExist(t *testing.T) {
	// var db mockDB
	exists := authentication.UserExists("<EMAIL>", nil) // You would inject mockDB in a real test
	if exists {
		t.Error("expected user to not exist")
	}
}
