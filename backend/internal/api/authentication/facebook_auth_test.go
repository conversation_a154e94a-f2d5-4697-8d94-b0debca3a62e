package authentication

import (
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestRedirectToFacebookLogin(t *testing.T) {
	req := httptest.NewRequest("GET", "/auth/facebook/login", nil)
	w := httptest.NewRecorder()

	RedirectToFacebookLogin(w, req)
	resp := w.Result()

	if resp.StatusCode != http.StatusTemporaryRedirect {
		t.Errorf("expected status %d, got %d", http.StatusTemporaryRedirect, resp.StatusCode)
	}
	location := resp.Header.Get("Location")
	if location == "" {
		t.Error("expected redirect location, got empty string")
	}
}

// Note: For HandleFacebookCallback, you would need to mock Facebook's API responses and the database.
// Here is a basic structure for such a test (not a full implementation):

func TestHandleFacebookCallback_NoCode(t *testing.T) {
	req := httptest.NewRequest("GET", "/auth/facebook/callback", nil)
	w := httptest.NewRecorder()

	// Pass nil for db since we are testing the code branch where code is missing
	HandleFacebookCallback(w, req, nil)
	resp := w.Result()

	if resp.StatusCode != http.StatusBadRequest {
		t.Errorf("expected status %d, got %d", http.StatusBadRequest, resp.StatusCode)
	}
}
